{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block title %} {% trans "Libro Mayor" %} {% endblock title %}

{% block stylesheets %}
  <!-- Estilos base -->
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css" />
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css" />

  <!-- DataTables -->
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.dataTables-v2.0.8.css">
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap.min-v5.1.3.css">

  <!-- DateRangePicker -->
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/daterangepicker/daterangepicker.min-v3.1.0.css" />

  <!-- Select2 -->
  <link href="{{ STATIC_URL }}assets/cdns_locals/css/select/select2.min-v4.1.0.css" rel="stylesheet" />
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/select/select2-bootstrap-5-theme.min-v5.css" />

  <style>
    .dataTables_wrapper .dataTables_filter {
      display: none;
    }

    .custom-filter {
      margin-bottom: 1rem;
    }

    .table-hover tbody tr:hover {
      background-color: rgba(0,0,0,.075);
    }

    .text-right {
      text-align: right !important;
    }

    .text-bold {
      font-weight: bold;
    }

    .balance-positive {
      color: #198754;
    }

    .balance-negative {
      color: #dc3545;
    }

    .loading-indicator {
      text-align: center;
      padding: 20px;
      display: none;
    }

    .account-header {
      background-color: #f8f9fa;
      padding: 10px;
      margin-bottom: 15px;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* Control de altura del Select2 */
    .select2-container--bootstrap-5 .select2-selection {
      max-height: 38px;
      overflow: hidden;
    }

    .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__rendered {
      display: inline-block;
      max-height: 38px;
      overflow: hidden;
      white-space: nowrap;
    }

    .select2-dropdown {
      max-height: 300px;
      overflow-y: auto;
    }

    /* Estilo para los checkboxes */
    .account-dropdown {
      position: relative;
    }

    .account-dropdown-toggle {
      width: 100%;
      text-align: left;
      position: relative;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .account-dropdown-toggle::after {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
    }

    /* Corrección del icono para evitar padding doble */
    .account-dropdown-toggle i.fas {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
    }

    .account-checkbox-list {
      position: absolute;
      width: 100%;
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
      padding: 0.5rem;
      background-color: white;
      z-index: 1000;
      display: none;
      top: 100%;
      left: 0;
      box-shadow: 0 5px 10px rgba(0,0,0,0.2);
    }

    .account-checkbox-item {
      margin-bottom: 0.5rem;
    }

    .select-buttons {
      margin-bottom: 0.5rem;
    }

    .account-selection-summary {
      font-size: 0.9rem;
      margin-top: 0.25rem;
      color: #6c757d;
    }

    /* Estilos para la distribución horizontal de la barra de filtros */
    /* Se elimina .filters-container y sus reglas hijas, ahora se usa Bootstrap grid */
    /* .filters-container {
      display: flex;
      align-items: flex-end;
      gap: 15px;
    }
    .filters-container > * {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
    } */

    /* Ajuste para el rango de fechas */
    /* Se elimina .date-range-container, se maneja con col-* */
    /* .date-range-container {
      flex: 1;
      min-width: calc(33% + 0.5rem);
    } */

    .date-range-input {
      text-align: center;
    }

    .date-range-auto-width {
      width: auto;
      min-width: 180px;
      max-width: 100%;
      display: inline-block;
      text-align: center;
    }

    /* Ajuste del botón de búsqueda */
    /* Se elimina .search-button-container */
    /* .search-button-container {
      min-width: 120px;
    } */

    /* Contenedor de exportación */
    /* Se elimina .export-container */
    /* .export-container {
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
    } */

    /* Ajuste fino para alineación vertical al pixel */
    .align-pixel-fix {
        transform: translateY(4px);
    }

    /* Estilo específico para el botón de filtros */
    #toggle-filters {
        transition: background-color 0.3s ease;
    }
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
  <div class="page-block">
    <div class="row align-items-center">
      <div class="col-md-12">
        <div class="page-header-title">
          <h5 class="m-b-10">
            <a href="javascript:history.back()">
              <i class="feather icon-arrow-left"></i>
            </a> &nbsp;
            {% trans "Libro Mayor" %}
          </h5>
        </div>
        <ul class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="{% url 'home' %}">
              <i class="feather icon-home"></i>
            </a>
          </li>
          <li class="breadcrumb-item">
            <a href="{% url 'app_sellers:summary' seller.shortname %}">{{ seller.name }}</a>
          </li>
          <li class="breadcrumb-item">
            <a href="#">{% trans "Libro Mayor" %}</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="row">
  <div class="col-sm-12">
    <div class="card">
      <div class="card-header">
        <!-- Usar Bootstrap Grid para alinear filtros -->
        {% include "banks/partials/_filters.html" %}
        <!-- Mostrar el resumen de selección aquí, debajo de la fila principal -->
        <div class="row">
            <div class="col-md-4">
                 {% include "banks/partials/_account_selection_summary.html" %}
            </div>
        </div>
      </div>

      <div class="card-body">
        <!-- Información de la cuenta -->
        {% include "banks/partials/_account_info.html" %}
        <!-- Tabla de movimientos -->
        <div class="table-responsive">
          {% include "banks/partials/_ledger_table.html" %}
          <!-- Indicador de carga -->
          {% include "banks/partials/_loading_indicator.html" %}
          <!-- Botón "Cargar más" -->
          {% include "banks/partials/_load_more_button.html" %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block javascripts %}
<!-- jQuery -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>

<!-- Bootstrap JS (después de jQuery, antes de DataTables) -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/bootstrap/bootstrap.bundle.min-v5.1.3.js"></script>

<!-- DataTables -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables-v2.0.8.js"></script>

<!-- Otros plugins -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/moment/moment.min-v2.29.4.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/moment/locale/es.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/daterangepicker/daterangepicker.min-v3.1.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/select/select2.min-v4.1.0.js"></script>

<script>
  // Variables globales para las URLs AJAX y exportación
  window.LEDGER_AJAX_URL = "{% url 'app_banks:ledger_data' seller.shortname %}";
  window.LEDGER_EXPORT_URL = "{% url 'app_banks:ledger_export' seller.shortname %}";
</script>
<script src="{% static 'assets/js/banks/ledger_list.js' %}"></script>
{% endblock javascripts %}
